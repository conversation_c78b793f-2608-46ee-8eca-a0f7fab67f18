#!/usr/bin/env python3
"""
Test script to verify the space calculation fix for Freecell
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from freecell import game_update, new_game
from playcard import get_rank, is_red

def create_test_scenario():
    """Create a test scenario similar to the one in the screenshot"""
    session = {'modified': False}
    
    # Create a custom game state that mimics the screenshot
    session['game_state'] = {
        'cells': [['JH'], [], [], []],  # One cell occupied
        'piles': [['AH'], ['AS'], ['AD'], []],  # Three foundations with Aces
        'tableau': [
            ['4S', '9S', 'AS', 'JS', '6D', '8D', 'JH'],  # Column 0
            ['2S', 'KS', 'TH', '8H', '3D', '2S'],        # Column 1  
            ['JS', '8S', '3S', '2H'],                     # Column 2
            ['KH', '9H', '6H'],                           # Column 3
            ['QS', '8S', '4S', '3H'],                     # Column 4
            ['9S', 'JD', '5S', '4H', '3S'],              # Column 5
            ['TS', '2H', 'TD', '5H'],                     # Column 6
            ['KD', '7S', '3H', '6S', '5D', '4S', '3H']   # Column 7
        ],
        'game_over': False,
        'message': '',
        'message_class': ''
    }
    
    return session

def test_space_calculation():
    """Test the space calculation with the fixed logic"""
    print("Testing space calculation fix...")
    
    session = create_test_scenario()
    
    # Print initial state
    game_state = session['game_state']
    print(f"Initial state:")
    print(f"  Occupied cells: {sum(1 for cell in game_state['cells'] if cell)}")
    print(f"  Free cells: {sum(1 for cell in game_state['cells'] if not cell)}")
    print(f"  Empty columns: {sum(1 for col in game_state['tableau'] if not col)}")
    print(f"  Column lengths: {[len(col) for col in game_state['tableau']]}")
    
    # Try to move from column 6 to column 1 (similar to screenshot scenario)
    print(f"\nAttempting to move from column 6 to column 1...")
    print(f"Source column 6 has {len(game_state['tableau'][6])} cards")
    print(f"Target column 1 has {len(game_state['tableau'][1])} cards")
    
    # Calculate expected free spaces
    free_cells = sum(1 for cell in game_state['cells'] if not cell)
    free_columns = sum(1 for i, col in enumerate(game_state['tableau']) if not col and i != 1)
    max_movable = (free_cells + 1) * (2 ** free_columns)
    
    print(f"Free cells: {free_cells}")
    print(f"Free columns (excluding target): {free_columns}")
    print(f"Max movable cards: {max_movable}")
    
    # Perform the move
    game_update(session, 'tt61')
    
    print(f"Result: {game_state['message']}")
    print(f"Message class: {game_state.get('message_class', 'info')}")

def test_empty_column_move():
    """Test moving to an empty column"""
    print("\n" + "="*50)
    print("Testing move to empty column...")
    
    session = create_test_scenario()
    
    # Make column 1 empty for testing
    session['game_state']['tableau'][1] = []
    
    game_state = session['game_state']
    print(f"Target column 1 is now empty")
    
    # Calculate spaces
    free_cells = sum(1 for cell in game_state['cells'] if not cell)
    free_columns = sum(1 for i, col in enumerate(game_state['tableau']) if not col and i != 1)
    max_movable = (free_cells + 1) * (2 ** free_columns)
    
    print(f"Free cells: {free_cells}")
    print(f"Free columns (excluding target): {free_columns}")
    print(f"Max movable cards: {max_movable}")
    
    # Try to move from column 6 to empty column 1
    game_update(session, 'tt61')
    
    print(f"Result: {game_state['message']}")
    print(f"Message class: {game_state.get('message_class', 'info')}")

def main():
    """Run all tests"""
    print("=== Freecell Space Calculation Tests ===\n")
    
    try:
        test_space_calculation()
        test_empty_column_move()
        
        print("\n=== Test Summary ===")
        print("Space calculation tests completed.")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
