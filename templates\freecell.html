{% extends "base.html" %}

{% block head %}
    <link rel="stylesheet" href="/static/freecell.css">
    <link rel="preload" href="/static/svg-cards.svg" as="image" type="image/svg+xml">
{% endblock %}

{% block main_content %}
    <div class="freecell-game">
        <!-- 顶部区域：空闲单元格和基础堆 -->
        <div class="top-row">
            <!-- 空闲单元格 -->
            <div class="free-cells">
                {% for cell_index, cell in enumerate(game_state['cells']) %}
                    <div class="cell-slot" data-cell-index="{{ cell_index }}">
                        {% if cell %}
                            <svg class="card" viewBox="0 0 169 244">
                                <use href="/static/svg-cards.svg#{{ get_card_name(cell[0]) }}"/>
                            </svg>
                        {% else %}
                            <div class="empty-cell"></div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>

            <!-- 基础堆 -->
            <div class="foundation-piles">
                {% for i, pile in enumerate(game_state['piles']) %}
                    <div class="foundation-slot pile{{ i }}" data-pile-index="{{ i }}">
                        {% if pile %}
                            <svg class="card" viewBox="0 0 169 244">
                                <use href="/static/svg-cards.svg#{{ get_card_name(pile[-1]) }}"/>
                            </svg>
                        {% else %}
                            <div class="empty-foundation"></div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- 游戏消息 -->
        {% if game_state['message'] %}
            <div class="game-message {{ game_state['message_class'] }}">
                {{ game_state['message'] }}
            </div>
        {% endif %}

        <!-- 牌列区域 -->
        <div class="tableau">
            <img class="highlight-arrow" src="/static/arrow-down.svg" alt="Down Arrow" style="display: none;">
            {% for column_index, column in enumerate(game_state['tableau']) %}
                <div class="tableau-column" data-column-index="{{ column_index }}">
                    {% if column %}
                        {% for card_index, card in enumerate(column) %}
                            <svg class="card" data-card-index="{{ card_index }}" viewBox="0 0 169 244">
                                <use href="/static/svg-cards.svg#{{ get_card_name(card) }}"/>
                            </svg>
                        {% endfor %}
                    {% else %}
                        <div class="empty-column"></div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
    <script src="/static/freecell.js"></script>
{% endblock %}
