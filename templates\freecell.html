{% extends "base.html" %}

{% block head %}
    <link rel="stylesheet" href="/static/freecell.css">
    <link rel="preload" href="/static/svg-cards.svg" as="image" type="image/svg+xml">
{% endblock %}

{% block main_content %}
    <div class="freecell-container">
        <div class="game-header">
            <h1>🎯 空当接龙</h1>
            <div class="game-info">
                {% if game_state['game_over'] %}
                    <span class="status-badge finished">🎉 游戏完成</span>
                {% else %}
                    <span class="status-badge playing">🎮 游戏进行中</span>
                {% endif %}
            </div>
        </div>

        <div class="top-area">
            <!-- 空闲单元格 -->
            <div class="cells-section">
                <h3>空闲单元格</h3>
                <div class="cells">
                    {% for cell_index, cell in enumerate(game_state['cells']) %}
                        <div class="cell" data-cell-index="{{ cell_index }}">
                            {% if cell %}
                                <svg class="card" viewBox="0 0 169 244">
                                    <use href="/static/svg-cards.svg#{{ get_card_name(cell[0]) }}"/>
                                </svg>
                            {% else %}
                                <div class="empty-slot">
                                    <span class="slot-label">空</span>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- 基础堆 -->
            <div class="piles-section">
                <h3>基础堆</h3>
                <div class="piles">
                    {% for i, pile in enumerate(game_state['piles']) %}
                        <div class="pile pile{{ i }}">
                            {% if pile %}
                                <svg class="card" viewBox="0 0 169 244">
                                    <use href="/static/svg-cards.svg#{{ get_card_name(pile[-1]) }}"/>
                                </svg>
                                <div class="pile-count">{{ pile|length }}</div>
                            {% else %}
                                <div class="empty-slot foundation-slot">
                                    <span class="suit-symbol">
                                        {% if i == 0 %}♠{% elif i == 1 %}♥{% elif i == 2 %}♣{% else %}♦{% endif %}
                                    </span>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 游戏消息 -->
        {% if game_state['message'] %}
            <div class="game-message {{ game_state['message_class'] }}">
                <div class="message-content">
                    <span class="message-icon">
                        {% if game_state['message_class'] == 'success' %}✅
                        {% elif game_state['message_class'] == 'error' %}❌
                        {% elif game_state['message_class'] == 'win' %}🎉
                        {% else %}💭
                        {% endif %}
                    </span>
                    <span class="message-text">{{ game_state['message'] }}</span>
                </div>
            </div>
        {% endif %}

        <!-- 牌列区域 -->
        <div class="tableau-section">
            <h3>牌列</h3>
            <div class="tableau">
                <img class="highlight-arrow" src="/static/arrow-down.svg" alt="Down Arrow" style="display: none;">
                {% for column_index, column in enumerate(game_state['tableau']) %}
                    <div class="tableau-column" data-column-index="{{ column_index }}">
                        <div class="column-header">
                            <span class="column-number">{{ column_index + 1 }}</span>
                        </div>
                        {% if column %}
                            {% for card_index, card in enumerate(column) %}
                                <svg class="card" data-card-index="{{ card_index }}" viewBox="0 0 169 244">
                                    <use href="/static/svg-cards.svg#{{ get_card_name(card) }}"/>
                                </svg>
                            {% endfor %}
                        {% else %}
                            <div class="empty-slot column-slot">
                                <span class="slot-label">空列</span>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- 游戏控制 -->
        <div class="game-controls">
            {% if game_state['game_over'] %}
                <a href="{{ url_for('new_game') }}" class="control-btn new-game">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">新游戏</span>
                </a>
            {% endif %}
            <a href="{{ url_for('rules') }}" class="control-btn rules">
                <span class="btn-icon">📖</span>
                <span class="btn-text">游戏规则</span>
            </a>
        </div>
    </div>
    <script src="/static/freecell.js"></script>
{% endblock %}
