{% extends "base.html" %}

{% block head %}
    <link rel="stylesheet" href="/static/freecell.css">
    <link rel="preload" href="/static/svg-cards.svg" as="image" type="image/svg+xml">
{% endblock %}

{% block main_content %}
    <div class="game-area">
    <h1>Freecell</h1>
    <div class="cells_and_piles">
        <div class="cells">
            {% for cell_index, cell in enumerate(game_state['cells']) %}
                <div class="cell" data-cell-index="{{ cell_index }}">
                    {% if cell %}
                        <svg class="card" viewBox="0 0 169 244">
                            <use href="/static/svg-cards.svg#{{ get_card_name(cell[0]) }}"/>
                        </svg>
                    {% else %}
                        <div class="empty-slot"></div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
        <div class="piles">
            {% for i, pile in enumerate(game_state['piles']) %}
                <div class="pile pile{{ i }}">
                    {% if pile %}
                        <svg class="card" viewBox="0 0 169 244">
                            <use href="/static/svg-cards.svg#{{ get_card_name(pile[-1]) }}"/>
                        </svg>
                    {% else %}
                        <div class="empty-slot"></div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
    <div class="tableau">
        <img class="highlight-arrow" src="/static/arrow-down.svg" alt="Down Arrow" style="display: none;">
        {% for column_index, column in enumerate(game_state['tableau']) %}
            <div class="tableau-column" data-column-index="{{ column_index }}">
                {% if column %}
                    {% for card_index, card in enumerate(column) %}
                        <svg class="card" data-card-index="{{ card_index }}" viewBox="0 0 169 244">
                            <use href="/static/svg-cards.svg#{{ get_card_name(card) }}"/>
                        </svg>
                    {% endfor %}
                {% else %}
                    <div class="empty-slot"></div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
    <script src="/static/freecell.js"></script>
    {% if game_state['message'] %}
        <div class="message {{ game_state['message_class'] }}">
            <h3>{{ game_state['message'] }}</h3>
        </div>
    {% endif %}
{% endblock %}
