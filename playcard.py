# constants and functions for playing cards

SUITS = 'HSDC'
RANK_MAP = {'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
            'T': 10, 'J': 11, 'Q': 12, 'K': 13}

# Short name of the cards is to comply with the community
# card is 2-character string, card[0] is rank and card[1] is suit

def get_rank(card):
    return RANK_MAP[card[0]]

def is_red(card):
    return card[1] in 'HD'

def make_deck():
    return [rank+suit for suit in SUITS for rank in RANK_MAP]

# Full name of the cards is to comply with <PERSON><PERSON><PERSON><PERSON><PERSON>'s so that we can reference his svg
SUIT_NAME_MAP = {'H':'heart', 'S':'spade', 'D':'diamond', 'C':'club'}
RANK_NAME_MAP = {'A':'1', '2':'2', '3':'3', '4':'4', '5':'5', '6':'6', '7':'7', '8':'8', '9':'9',
         'T':'10', 'J': 'jack', 'Q': 'queen', 'K': 'king'}

def get_card_name(card):
    return f'{SUIT_NAME_MAP[card[1]]}_{RANK_NAME_MAP[card[0]]}'