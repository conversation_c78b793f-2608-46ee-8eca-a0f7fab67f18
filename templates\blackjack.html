{% extends "base.html" %}

{% block head %}
    <link rel="stylesheet" href="/static/blackjack.css">
    <link rel="preload" href="/static/svg-cards.svg" as="image" type="image/svg+xml">
{% endblock %}

{% block main_content %}
    <div class="blackjack-container">
        <div class="game-header">
            <h1>🃏 21点</h1>
            <div class="game-status">
                {% if game_state['game_over'] %}
                    <span class="status-badge finished">游戏结束</span>
                {% else %}
                    <span class="status-badge playing">游戏进行中</span>
                {% endif %}
            </div>
        </div>

        <div class="game-board">
            <!-- 庄家区域 -->
            <div class="dealer-section">
                <div class="player-info">
                    <div class="player-avatar">🎩</div>
                    <div class="player-details">
                        <h3>庄家</h3>
                        <div class="score-display">
                            <span class="score-label">点数:</span>
                            <span class="score-value">{{ game_state['dealer_value'] }}</span>
                        </div>
                    </div>
                </div>
                <div class="cards-container">
                    {% if game_state['game_over'] %}
                        {% for card in game_state['dealer_hand'] %}
                            <div class="card-wrapper">
                                <svg class="card" viewBox="0 0 169 244">
                                    <use href="/static/svg-cards.svg#{{ get_card_name(card) }}"/>
                                </svg>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="card-wrapper">
                            <svg class="card" viewBox="0 0 169 244">
                                <use href="/static/svg-cards.svg#{{ get_card_name(game_state['dealer_hand'][0]) }}"/>
                            </svg>
                        </div>
                        <div class="card-wrapper hidden-card">
                            <svg class="card" viewBox="0 0 169 244">
                                <use href="/static/svg-cards.svg#back"/>
                            </svg>
                            <div class="hidden-overlay">?</div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- 游戏消息 -->
            {% if game_state['message'] %}
                <div class="game-message {{ game_state['message_class'] }}">
                    <div class="message-content">
                        <span class="message-icon">
                            {% if game_state['message_class'] == 'success' %}🎉
                            {% elif game_state['message_class'] == 'error' %}💥
                            {% else %}💭
                            {% endif %}
                        </span>
                        <span class="message-text">{{ game_state['message'] }}</span>
                    </div>
                </div>
            {% endif %}

            <!-- 玩家区域 -->
            <div class="player-section">
                <div class="player-info">
                    <div class="player-avatar">👤</div>
                    <div class="player-details">
                        <h3>您</h3>
                        <div class="score-display">
                            <span class="score-label">点数:</span>
                            <span class="score-value {{ 'bust' if game_state['player_value'] > 21 else '' }}">
                                {{ game_state['player_value'] }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="cards-container">
                    {% for card in game_state['player_hand'] %}
                        <div class="card-wrapper">
                            <svg class="card" viewBox="0 0 169 244">
                                <use href="/static/svg-cards.svg#{{ get_card_name(card) }}"/>
                            </svg>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="game-actions">
            {% if not game_state['game_over'] %}
                <a href="{{ url_for('game_update', action='hit') }}" class="action-btn hit-btn">
                    <span class="btn-icon">🎯</span>
                    <span class="btn-text">要牌 (Hit)</span>
                </a>
                <a href="{{ url_for('game_update', action='stand') }}" class="action-btn stand-btn">
                    <span class="btn-icon">✋</span>
                    <span class="btn-text">停牌 (Stand)</span>
                </a>
            {% else %}
                <a href="{{ url_for('new_game') }}" class="action-btn new-game-btn">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">新游戏</span>
                </a>
            {% endif %}
        </div>
    </div>
{% endblock %}