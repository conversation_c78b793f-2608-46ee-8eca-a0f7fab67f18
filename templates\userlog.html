{% extends "base.html" %}

{% block main_content %}
    <div class="log-container">
        <div class="log-header">
            <h2>游戏日志</h2>
            <div class="log-controls">
                <button onclick="scrollToTop()" class="control-btn">↑ 顶部</button>
                <button onclick="scrollToBottom()" class="control-btn">↓ 底部</button>
                <button onclick="clearLog()" class="control-btn danger">清空日志</button>
            </div>
        </div>
        <div class="log-panel" id="log-panel">
            <pre>{{ log if log else '暂无日志记录' }}</pre>
        </div>
    </div>
    <script>
        // Scroll to the bottom of the log panel on page load
        document.addEventListener('DOMContentLoaded', function () {
            const logPanel = document.getElementById('log-panel');
            logPanel.scrollTop = logPanel.scrollHeight;
        });

        function scrollToTop() {
            const logPanel = document.getElementById('log-panel');
            logPanel.scrollTop = 0;
        }

        function scrollToBottom() {
            const logPanel = document.getElementById('log-panel');
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        function clearLog() {
            if (confirm('确定要清空所有日志吗？此操作不可撤销。')) {
                fetch('/clear_log', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('清空日志失败');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('清空日志失败');
                    });
            }
        }
    </script>
{% endblock %}