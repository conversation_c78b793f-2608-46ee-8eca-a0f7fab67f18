/* Reset default margins and ensure box-sizing */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Ensure the body takes the full viewport height */
body {
    width: 100vw;
    min-height: 100vh;
    display: flex;
    flex-direction: column; /* Stack top and bottom sections vertically */
    background-color: #ffffff;
    overflow-x: auto; /* Allow horizontal scrolling if content overflows */
    font-family: 'Arial', sans-serif;
    padding: 8px;
}

/* Top section styling */
.top-area {
    background-color: #2196F3;        /* Blue for visibility */
    color: white;
    padding: 20px;
    display: flex; /* Make top-area a flex container */
    justify-content: space-between; /* Push h1 left, menu right */
    align-items: center; /* Vertically center h1 and menu */
    flex: 0 0 auto; /* Fixed height based on content */
    border-top-left-radius: 10px; /* Round top-left corner */
    border-top-right-radius: 10px; /* Round top-right corner */
    border-bottom-left-radius: 0; /* Keep bottom-left corner rectangular */
    border-bottom-right-radius: 0; /* Keep bottom-right corner rectangular */
}

/* Style the top-heading (h1) */
.top-heading {
    font-size: 2rem; /* Adjust size as needed */
    margin: 0; /* Remove default margin for alignment */
}


/* Style the menu */
.menu ul {
    display: flex; /* Makes menu items horizontal */
    list-style: none; /* Removes default bullets */
    margin: 0; /* Remove default margin for alignment */
    padding: 0; /* Remove default padding */
}

.menu li {
    margin-left: 1rem; /* Space between menu items */
}

.menu a {
    text-decoration: none; /* Removes underline from links */
    color: white; /* White for visibility against blue background */
    font-size: 16px; /* Adjust size as needed */
}

.menu a:hover {
    color: #ccc; /* Light gray on hover for contrast, adjust as needed */
}

.menu a.active {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 5px;
    font-weight: bold;
}

/* Style the text-box */
.text-box {
    padding: 2rem; /* Spacing around content */
    margin: 0 auto; /* Centers the content */
}

.text-box h2 {
    font-size: 1.5rem; /* Slightly smaller than top-heading */
    margin-bottom: 1rem; /* Space below heading */
}

.text-box p {
    margin-bottom: 1rem; /* Space between paragraphs */
}

.text-box ul {
    margin-bottom: 1rem; /* Space between paragraphs */
}

.text-box a {
    color: #007bff; /* Blue for links, matches hover color of menu */
    text-decoration: none;
}

.text-box a:hover {
    text-decoration: underline; /* Underline on hover for clarity */
}

.text-box .item-title {
    font-weight: bold;
}

/* Select page styles */
.select-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.select-header {
    text-align: center;
    margin-bottom: 3rem;
}

.select-header h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.warning-banner {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 0.8rem 1.5rem;
    color: #856404;
    font-weight: 500;
}

.warning-icon {
    font-size: 1.2rem;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.game-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.game-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.game-card.active {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: 3px solid #fff;
}

.game-card:nth-child(2) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.game-card:nth-child(2).active {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.game-icon {
    font-size: 4rem;
    text-align: center;
    margin-bottom: 1rem;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

.game-content {
    text-align: center;
}

.game-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.game-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.game-features {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.feature-tag {
    background-color: rgba(255,255,255,0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.game-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.stat-value {
    font-weight: bold;
    font-size: 1rem;
}

.difficulty-bar {
    width: 80px;
    height: 6px;
    background-color: rgba(255,255,255,0.3);
    border-radius: 3px;
    overflow: hidden;
}

.difficulty-fill {
    height: 100%;
    background-color: #fff;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.current-game-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: rgba(255,255,255,0.9);
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}



.game-area {
    background-color: #007000;
    color: #fff;
    padding: 20px;
    border-top-left-radius: 0; /* Rectangular top-left corner */
    border-top-right-radius: 0; /* Rectangular top-right corner */
    border-bottom-left-radius: 10px; /* Round bottom-left corner */
    border-bottom-right-radius: 10px; /* Round bottom-right corner */
    min-height: calc(100vh - 120px);
}

.game-area h1 {
    text-align: center;
    font-size: 2.5rem;
    margin: 0 0 30px 0;
    padding: 15px 0;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.log-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 120px);
    margin: 1vw;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.log-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.log-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background-color: #fff;
    color: #333;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.control-btn.danger {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.control-btn.danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.log-panel {
    flex-grow: 1;
    padding: 20px;
    background-color: #f8f9fa;
    overflow-y: auto;
    overflow-x: auto;
    font-family: 'Courier New', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
}

.log-panel pre {
    margin: 0;
    padding: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    background: transparent;
    border: none;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.log-entry {
    margin: 0;
    padding: 2px 0;
    font-size: 0.9em;
    line-height: 1.2;
    color: #000;
    font-family: Consolas, monospace; /* Monospaced font */
}

/* About page styles */
.about-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.about-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #2196F3;
}

.about-header h2 {
    margin: 0;
    color: #333;
    font-size: 2rem;
}

.version-badge {
    background-color: #2196F3;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

.about-content {
    display: grid;
    gap: 1.5rem;
}

.about-section {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #2196F3;
}

.about-section h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.3rem;
}

.about-section p {
    margin: 0 0 0.5rem 0;
    line-height: 1.6;
    color: #555;
}

.game-list, .feature-list {
    margin: 0;
    padding-left: 1.5rem;
}

.game-list li, .feature-list li {
    margin-bottom: 0.5rem;
    color: #555;
}

.contact-email {
    color: #2196F3;
    text-decoration: none;
    font-weight: bold;
    padding: 0.3rem 0.6rem;
    background-color: rgba(33, 150, 243, 0.1);
    border-radius: 5px;
    transition: all 0.2s ease;
}

.contact-email:hover {
    background-color: rgba(33, 150, 243, 0.2);
    text-decoration: none;
}

.error-message {
    text-align: center;
    padding: 3rem;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #dc3545;
}

.error-message h2 {
    color: #dc3545;
    margin-bottom: 1rem;
}

.back-button {
    display: inline-block;
    margin-top: 1rem;
    padding: 0.8rem 1.5rem;
    background-color: #2196F3;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.2s ease;
}

.back-button:hover {
    background-color: #1976D2;
    text-decoration: none;
}

@media (max-width: 600px) {
    .top-area {
        flex-direction: column;
        align-items: flex-start;
    }
    .menu ul {
        flex-direction: column;
        margin-top: 10px;
    }
    .menu li {
        margin-left: 0;
        margin-bottom: 10px;
    }

    .about-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .about-header h2 {
        font-size: 1.5rem;
    }

    .about-container {
        margin: 1rem auto;
    }

    /* Select page mobile styles */
    .select-container {
        padding: 1rem;
    }

    .select-header h2 {
        font-size: 2rem;
    }

    .games-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .game-card {
        padding: 1.5rem;
    }

    .game-icon {
        font-size: 3rem;
    }

    .game-title {
        font-size: 1.5rem;
    }

    .game-description {
        font-size: 1rem;
    }

    .game-stats {
        flex-direction: column;
        gap: 1rem;
    }
}
