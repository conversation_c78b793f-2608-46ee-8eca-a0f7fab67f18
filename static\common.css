/* Reset default margins and ensure box-sizing */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Ensure the body takes the full viewport height */
body {
    width: 100vw;
    min-height: 100vh;
    display: flex;
    flex-direction: column; /* Stack top and bottom sections vertically */
    background-color: #ffffff;
    overflow-x: auto; /* Allow horizontal scrolling if content overflows */
    font-family: 'Arial', sans-serif;
    padding: 8px;
}

/* Top section styling */
.top-area {
    background-color: #2196F3;        /* Blue for visibility */
    color: white;
    padding: 20px;
    display: flex; /* Make top-area a flex container */
    justify-content: space-between; /* Push h1 left, menu right */
    align-items: center; /* Vertically center h1 and menu */
    flex: 0 0 auto; /* Fixed height based on content */
    border-top-left-radius: 10px; /* Round top-left corner */
    border-top-right-radius: 10px; /* Round top-right corner */
    border-bottom-left-radius: 0; /* Keep bottom-left corner rectangular */
    border-bottom-right-radius: 0; /* Keep bottom-right corner rectangular */
}

/* Style the top-heading (h1) */
.top-heading {
    font-size: 2rem; /* Adjust size as needed */
    margin: 0; /* Remove default margin for alignment */
}


/* Style the menu */
.menu ul {
    display: flex; /* Makes menu items horizontal */
    list-style: none; /* Removes default bullets */
    margin: 0; /* Remove default margin for alignment */
    padding: 0; /* Remove default padding */
}

.menu li {
    margin-left: 1rem; /* Space between menu items */
}

.menu a {
    text-decoration: none; /* Removes underline from links */
    color: white; /* White for visibility against blue background */
    font-size: 16px; /* Adjust size as needed */
}

.menu a:hover {
    color: #ccc; /* Light gray on hover for contrast, adjust as needed */
}

.menu a.active {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 5px;
    font-weight: bold;
}

/* Style the text-box */
.text-box {
    padding: 2rem; /* Spacing around content */
    margin: 0 auto; /* Centers the content */
}

.text-box h2 {
    font-size: 1.5rem; /* Slightly smaller than top-heading */
    margin-bottom: 1rem; /* Space below heading */
}

.text-box p {
    margin-bottom: 1rem; /* Space between paragraphs */
}

.text-box ul {
    margin-bottom: 1rem; /* Space between paragraphs */
}

.text-box a {
    color: #007bff; /* Blue for links, matches hover color of menu */
    text-decoration: none;
}

.text-box a:hover {
    text-decoration: underline; /* Underline on hover for clarity */
}

.text-box .item-title {
    font-weight: bold;
}

h2.caution {
    padding: 10px;
 }

.game_pool > * {
    display: inline-block;
    padding: 15px 30px;
    background-color: forestgreen;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    margin: 10px;
}

.game_pool > .active {
    background-color: mediumvioletred;
}

.game_pool > *:hover {
    transform: translateY(-4px);
}

.game-area {
    background-color: #007000;
    color: #fff;
    padding: 8px;
    border-top-left-radius: 0; /* Rectangular top-left corner */
    border-top-right-radius: 0; /* Rectangular top-right corner */
    border-bottom-left-radius: 10px; /* Round bottom-left corner */
    border-bottom-right-radius: 10px; /* Round bottom-right corner */
}

.log-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 120px);
    margin: 1vw;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.log-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.log-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background-color: #fff;
    color: #333;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.control-btn.danger {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.control-btn.danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.log-panel {
    flex-grow: 1;
    padding: 20px;
    background-color: #f8f9fa;
    overflow-y: auto;
    overflow-x: auto;
    font-family: 'Courier New', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
}

.log-panel pre {
    margin: 0;
    padding: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    background: transparent;
    border: none;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.log-entry {
    margin: 0;
    padding: 2px 0;
    font-size: 0.9em;
    line-height: 1.2;
    color: #000;
    font-family: Consolas, monospace; /* Monospaced font */
}

@media (max-width: 600px) {
    .top-area {
        flex-direction: column;
        align-items: flex-start;
    }
    .menu ul {
        flex-direction: column;
        margin-top: 10px;
    }
    .menu li {
        margin-left: 0;
        margin-bottom: 10px;
    }
}
