{% extends "base.html" %}

{% block main_content %}
    <div class="text-box">
        {% if cur_game=="blackjack" %}
            <h2>21点游戏规则：</h2>
            <ul>
                <li>游戏使用标准的52张牌。</li>
                <li>游戏开始时每位玩家获得两张牌。</li>
                <li>庄家最初只显示一张牌。</li>
                <li>您可以选择要牌（Hit）或停牌（Stand）。</li>
                <li>如果您的点数超过21点，立即输掉游戏（爆牌）。</li>
                <li>庄家必须要牌直到达到至少17点。</li>
                <li>如果庄家爆牌，您获胜。</li>
                <li>如果双方都没有爆牌，点数较高的一方获胜。</li>
            </ul>
        {% elif cur_game=="freecell" %}
            <h2>空当接龙游戏规则</h2>
            <p>空当接龙是一种流行的单人纸牌游戏，游戏开始时所有牌都正面朝上。目标是将所有牌移动到四个基础堆中，遵循特定的游戏规则。</p>

            <h3>游戏目标：</h3>
            <p>空当接龙的目标是将所有52张牌按花色从A到K的顺序移动到基础堆中，同时遵循游戏的移动规则。</p>

            <h3>游戏设置：</h3>
            <ul>
                <li><span class="item-title">牌组：</span> 游戏使用标准的52张牌。</li>
                <li><span class="item-title">牌列：</span> 游戏板由八列牌组成，每列最初都有一定数量的牌。</li>
                <li><span class="item-title">基础堆：</span> 四个基础堆位于右上角，开始时为空。牌将按升序移动到这里，每个花色从A开始。</li>
                <li><span class="item-title">空闲单元格：</span> 有四个空闲单元格可用，每个单元格可以临时放置一张牌来协助移动其他牌。</li>
            </ul>

            <h3>移动规则：</h3>
            <ul>
                <li><span class="item-title">牌列间移动：</span> 一张牌可以从一列移动到另一列，前提是它被放置在相反颜色且大一点的牌上。例如，红色6可以放在黑色7上。</li>
                <li><span class="item-title">移动到基础堆：</span> 如果一张牌是序列中的下一张牌，可以移动到基础堆，从A开始。例如，红桃2可以放在红桃A上。</li>
                <li><span class="item-title">移动到空闲单元格：</span> 一张牌可以放置在空闲单元格中，但每个空闲单元格同时只能放置一张牌。</li>
                <li><span class="item-title">多张牌移动：</span> 如果多张牌按降序排列且颜色交替，可以一起移动到有效的牌列中。例如，红色8、黑色7和红色6的序列可以作为一组移动。</li>
            </ul>

            <h3>获胜条件：</h3>
            <ul>
                <li><span class="item-title">目标：</span> 当所有牌都按花色从A到K的顺序放置在基础堆中时，游戏获胜。</li>
                <li><span class="item-title">无法移动：</span> 如果没有更多有效移动，游戏失败。</li>
            </ul>

            <h3>附加说明：</h3>
            <ul>
                <li><span class="item-title">撤销：</span> 在大多数版本中，玩家可以撤销移动来纠正错误，但这取决于游戏的具体实现。</li>
                <li><span class="item-title">策略：</span> 提前规划移动很重要，特别是有效管理空闲单元格和牌列。</li>
            </ul>
        {% endif %}
    </div>
{% endblock %}
