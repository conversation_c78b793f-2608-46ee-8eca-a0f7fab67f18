{% extends "base.html" %}

{% block main_content %}
    <div class="text-box">
        {% if cur_game=="blackjack" %}
            <h2>Blackjack Rules:</h2>
            <ul>
                <li>The game is played with a standard deck of 52 cards.</li>
                <li>Each player receives two cards at the beginning of the game.</li>
                <li>The dealer only reveals one of their cards initially.</li>
                <li>You can choose to take more cards (hit) or stay with your current cards (stand).</li>
                <li>If you go over 21 points, you immediately lose (bust).</li>
                <li>The dealer must hit until they reach at least 17 points.</li>
                <li>If the dealer busts, you win.</li>
                <li>If neither player busts, the one with the highest total wins.</li>
            </ul>
        {% elif cur_game=="freecell" %}
            <h2>FreeCell Solitaire Rules</h2>
            <p>FreeCell is a popular solitaire card game where all the cards are dealt face-up at the beginning. The
                goal is
                to move all cards to the four foundation piles, following specific rules of play.</p>

            <h3>Objective:</h3>
            <p>The objective of FreeCell is to move all 52 cards into the foundation piles, organized by suit, from Ace
                to
                King, while following the game’s movement rules.</p>

            <h3>Setup:</h3>
            <ul>
                <li><span class="item-title">Deck:</span> The game uses a standard 52-card deck.</li>
                <li><span class="item-title">Tableau:</span> The game board consists of eight tableau columns, each
                    initially holding a set of
                    cards.
                </li>
                <li><span class="item-title">Foundation Piles:</span> Four foundation piles are placed at the top-right
                    corner, starting empty.
                    Cards will be moved here in ascending order, starting with the Ace of each suit.
                </li>
                <li><span class="item-title">Free Cells:</span> There are four free cells available where one card can
                    be temporarily placed to
                    assist in maneuvering other cards.
                </li>
            </ul>

            <h3>Movement Rules:</h3>
            <ul>
                <li><span class="item-title">Tableau to Tableau:</span> A card can be moved from one tableau column to
                    another if it is placed
                    on a card of the opposite color and one rank higher. For example, a red 6 can be placed on a black
                    7.
                </li>
                <li><span class="item-title">Card to Foundation:</span> A card can be moved to the foundation pile if it
                    is the next card in
                    sequence, starting with an Ace. For example, the 2 of Hearts can be placed on the Ace of Hearts.
                </li>
                <li><span class="item-title">Card to Free Cell:</span> A card can be placed in one of the free cells,
                    but only one card can be
                    in each free cell at any time.
                </li>
                <li><span class="item-title">Moving Multiple Cards:</span> Multiple cards can be moved together if they
                    are placed on a valid
                    tableau column in descending order and alternating colors. For example, a sequence of red 8, black
                    7,
                    and red 6 can be moved as a group.
                </li>
            </ul>

            <h3>Winning the Game:</h3>
            <ul>
                <li><span class="item-title">Goal:</span> The game is won when all cards are placed in the foundation
                    piles, ordered from Ace
                    to King, by suit.
                </li>
                <li><span class="item-title">No Moves Left:</span> If there are no more valid moves, the game is lost.
                </li>
            </ul>

            <h3>Additional Notes:</h3>
            <ul>
                <li><span class="item-title">Undo:</span> In most versions, players can undo moves to correct mistakes,
                    but this depends on the
                    specific implementation of the game.
                </li>
                <li><span class="item-title">Strategy:</span> Planning moves ahead is important, especially for managing
                    free cells and tableau
                    columns efficiently.
                </li>
            </ul>
        {% endif %}
    </div>
{% endblock %}