#!/usr/bin/env python3
"""
Test script for Freecell game implementation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from freecell import game_update, new_game
from playcard import get_rank, is_red

def create_test_session():
    """Create a test session with a known game state"""
    session = {'modified': False}
    new_game(session)
    return session

def test_tableau_to_cell():
    """Test moving card from tableau to cell"""
    print("Testing Tableau to Cell movement...")
    session = create_test_session()
    
    # Try to move a card from column 0 to cell 0
    game_update(session, 'tc00')
    
    game_state = session['game_state']
    if game_state['cells'][0]:
        print("✓ Card successfully moved from tableau to cell")
        print(f"  Message: {game_state['message']}")
    else:
        print("✗ Failed to move card from tableau to cell")
        print(f"  Message: {game_state['message']}")

def test_cell_to_tableau():
    """Test moving card from cell to tableau"""
    print("\nTesting Cell to Tableau movement...")
    session = create_test_session()
    
    # First move a card to cell
    game_update(session, 'tc00')
    
    # Then try to move it back to an empty column (if available)
    for i in range(8):
        if not session['game_state']['tableau'][i]:
            game_update(session, f'ct0{i}')
            break
    else:
        # If no empty column, try to place on a valid card
        game_update(session, 'ct01')
    
    game_state = session['game_state']
    print(f"  Message: {game_state['message']}")

def test_foundation_placement():
    """Test moving cards to foundation"""
    print("\nTesting Foundation placement...")
    session = create_test_session()
    
    # Look for an Ace in the tableau
    ace_found = False
    for col_idx, column in enumerate(session['game_state']['tableau']):
        if column and get_rank(column[-1]) == 1:  # Found an Ace
            print(f"Found Ace in column {col_idx}")
            game_update(session, f'tf{col_idx}')
            ace_found = True
            break
    
    if not ace_found:
        print("No Ace found at bottom of any column")
        # Try moving any card to foundation (should fail)
        game_update(session, 'tf0')
    
    game_state = session['game_state']
    print(f"  Message: {game_state['message']}")

def test_tableau_to_tableau():
    """Test moving cards between tableau columns"""
    print("\nTesting Tableau to Tableau movement...")
    session = create_test_session()
    
    # Try to move cards between columns
    game_update(session, 'tt01')
    
    game_state = session['game_state']
    print(f"  Message: {game_state['message']}")

def test_invalid_moves():
    """Test various invalid moves"""
    print("\nTesting Invalid moves...")
    session = create_test_session()
    
    # Test invalid action format
    game_update(session, 'invalid')
    print(f"Invalid action: {session['game_state']['message']}")
    
    # Test moving from empty cell
    game_update(session, 'ct00')
    print(f"Empty cell move: {session['game_state']['message']}")
    
    # Test moving to occupied cell
    game_update(session, 'tc00')  # Move to cell 0
    game_update(session, 'tc10')  # Try to move to same cell
    print(f"Occupied cell move: {session['game_state']['message']}")

def print_game_state(session):
    """Print current game state for debugging"""
    game_state = session['game_state']
    print("\nCurrent Game State:")
    print("Cells:", [len(cell) for cell in game_state['cells']])
    print("Foundations:", [len(pile) for pile in game_state['piles']])
    print("Tableau columns:", [len(col) for col in game_state['tableau']])
    if game_state['message']:
        print(f"Message: {game_state['message']} ({game_state.get('message_class', 'info')})")

def main():
    """Run all tests"""
    print("=== Freecell Game Implementation Tests ===\n")
    
    try:
        test_tableau_to_cell()
        test_cell_to_tableau()
        test_foundation_placement()
        test_tableau_to_tableau()
        test_invalid_moves()
        
        print("\n=== Test Summary ===")
        print("All tests completed. Check the messages above for results.")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
