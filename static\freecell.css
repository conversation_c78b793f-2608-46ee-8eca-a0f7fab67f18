/* Windows-style Freecell Game */
.freecell-game {
    background-color: #008000; /* Classic Windows green */
    padding: 20px;
    min-height: 600px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-radius: 0; /* Windows style - no rounded corners */
}

/* Top row containing free cells and foundation piles */
.top-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 0 20px;
}

/* Free cells (left side) */
.free-cells {
    display: flex;
    gap: 15px;
}

.cell-slot {
    width: 80px;
    height: 115px;
    position: relative;
}

.empty-cell {
    width: 80px;
    height: 115px;
    border: 2px solid #004000;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Foundation piles (right side) */
.foundation-piles {
    display: flex;
    gap: 15px;
}

.foundation-slot {
    width: 80px;
    height: 115px;
    position: relative;
}

.empty-foundation {
    width: 80px;
    height: 115px;
    border: 2px solid #004000;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
}

/* Suit symbols for empty foundation piles */
.pile0 .empty-foundation::after {
    content: '♥';
    color: #ff4444;
    font-size: 24px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
}

.pile1 .empty-foundation::after {
    content: '♠';
    color: #333;
    font-size: 24px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
}

.pile2 .empty-foundation::after {
    content: '♦';
    color: #ff4444;
    font-size: 24px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
}

.pile3 .empty-foundation::after {
    content: '♣';
    color: #333;
    font-size: 24px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
}

/* Cards */
.card {
    width: 80px;
    height: 115px;
    cursor: pointer;
    border-radius: 8px;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    transition: transform 0.1s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.4);
}

/* Game message */
.game-message {
    text-align: center;
    padding: 10px 20px;
    margin: 10px auto;
    max-width: 400px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 14px;
}

.game-message.success {
    background-color: #ffff99;
    color: #333;
    border: 1px solid #cccc00;
}

.game-message.error {
    background-color: #ffcccc;
    color: #cc0000;
    border: 1px solid #cc0000;
}

.game-message.win {
    background-color: #ccffcc;
    color: #006600;
    border: 1px solid #006600;
}

/* Tableau (main playing area) */
.tableau {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 20px;
    position: relative;
}

.tableau-column {
    width: 80px;
    min-height: 115px;
    position: relative;
}

.tableau-column .card {
    position: absolute;
    left: 0;
    z-index: 1;
}

/* Stacking cards with overlap */
.tableau-column .card:nth-child(1) { top: 0px; z-index: 1; }
.tableau-column .card:nth-child(2) { top: 25px; z-index: 2; }
.tableau-column .card:nth-child(3) { top: 50px; z-index: 3; }
.tableau-column .card:nth-child(4) { top: 75px; z-index: 4; }
.tableau-column .card:nth-child(5) { top: 100px; z-index: 5; }
.tableau-column .card:nth-child(6) { top: 125px; z-index: 6; }
.tableau-column .card:nth-child(7) { top: 150px; z-index: 7; }
.tableau-column .card:nth-child(8) { top: 175px; z-index: 8; }
.tableau-column .card:nth-child(9) { top: 200px; z-index: 9; }
.tableau-column .card:nth-child(10) { top: 225px; z-index: 10; }
.tableau-column .card:nth-child(11) { top: 250px; z-index: 11; }
.tableau-column .card:nth-child(12) { top: 275px; z-index: 12; }
.tableau-column .card:nth-child(13) { top: 300px; z-index: 13; }
.tableau-column .card:nth-child(14) { top: 325px; z-index: 14; }
.tableau-column .card:nth-child(15) { top: 350px; z-index: 15; }
.tableau-column .card:nth-child(16) { top: 375px; z-index: 16; }
.tableau-column .card:nth-child(17) { top: 400px; z-index: 17; }
.tableau-column .card:nth-child(18) { top: 425px; z-index: 18; }
.tableau-column .card:nth-child(19) { top: 450px; z-index: 19; }
.tableau-column .card:nth-child(20) { top: 475px; z-index: 20; }

.empty-column {
    width: 80px;
    height: 115px;
    border: 2px dashed #004000;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.05);
}

/* Highlight arrow */
.highlight-arrow {
    position: absolute;
    width: 30px;
    height: 30px;
    z-index: 100;
    pointer-events: none;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
}

/* Responsive adjustments for smaller screens */
@media (max-width: 1200px) {
    .freecell-game {
        padding: 15px;
    }
    
    .tableau {
        gap: 8px;
    }
    
    .free-cells, .foundation-piles {
        gap: 10px;
    }
}

@media (max-width: 900px) {
    .card, .cell-slot, .foundation-slot, .empty-cell, .empty-foundation, .empty-column {
        width: 60px;
        height: 86px;
    }
    
    .tableau {
        gap: 6px;
    }
    
    .free-cells, .foundation-piles {
        gap: 8px;
    }
    
    .tableau-column .card:nth-child(1) { top: 0px; }
    .tableau-column .card:nth-child(2) { top: 18px; }
    .tableau-column .card:nth-child(3) { top: 36px; }
    .tableau-column .card:nth-child(4) { top: 54px; }
    .tableau-column .card:nth-child(5) { top: 72px; }
    .tableau-column .card:nth-child(6) { top: 90px; }
    .tableau-column .card:nth-child(7) { top: 108px; }
    .tableau-column .card:nth-child(8) { top: 126px; }
    .tableau-column .card:nth-child(9) { top: 144px; }
    .tableau-column .card:nth-child(10) { top: 162px; }
    .tableau-column .card:nth-child(11) { top: 180px; }
    .tableau-column .card:nth-child(12) { top: 198px; }
    .tableau-column .card:nth-child(13) { top: 216px; }
    .tableau-column .card:nth-child(14) { top: 234px; }
    .tableau-column .card:nth-child(15) { top: 252px; }
    .tableau-column .card:nth-child(16) { top: 270px; }
    .tableau-column .card:nth-child(17) { top: 288px; }
    .tableau-column .card:nth-child(18) { top: 306px; }
    .tableau-column .card:nth-child(19) { top: 324px; }
    .tableau-column .card:nth-child(20) { top: 342px; }
}
