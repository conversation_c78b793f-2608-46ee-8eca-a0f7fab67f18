import random
from playcard import make_deck, get_card_name
import game_logger


CARD_VALUES = {
    'A': 11,
    '2': 2,
    '3': 3,
    '4': 4,
    '5': 5,
    '6': 6,
    '7': 7,
    '8': 8,
    '9': 9,
    'T': 10,
    'J': 10,
    'Q': 10,
    'K': 10,
}

def calculate_hand_value(hand):
    """计算手牌总点数，自动处理A牌的1/11点逻辑"""
    value, aces = 0, 0
    for card in hand:
        rank = card[0]  # 卡牌格式为'AS'，rank是第一个字符
        value += CARD_VALUES[rank]
        aces += rank == 'A'  # 统计A牌数量

    # 当总点数超过21时，将A牌从11点调整为1点
    while value > 21 and aces > 0:
        value -= 10
        aces -= 1

    return value


def new_game(session):
    """初始化新游戏，创建牌组、发牌并设置初始状态

    根据21点规则：
    - 使用标准52张牌
    - 每位玩家开始时获得两张牌
    - 庄家初始只显示一张牌
    """
    deck = make_deck()
    random.shuffle(deck)

    player_hand = [deck.pop(), deck.pop()]
    dealer_hand = [deck.pop(), deck.pop()]

    session['game_state'] = {
        'deck': deck,
        'player_hand': player_hand,
        'dealer_hand': dealer_hand,
        'player_value': calculate_hand_value(player_hand),
        'dealer_value': calculate_hand_value([dealer_hand[0]]),  # 初始只计算明牌点数
        'game_over': False,
        'message': '选择要牌(Hit)或停牌(Stand)',
        'message_class': 'info'
    }

    # 记录新游戏开始
    session_id = session.get('session_id', '')
    if session_id:
        game_logger.log_blackjack_new_game(session_id, dealer_hand[0], player_hand)

    # 检查玩家是否有21点（Blackjack）
    if session['game_state']['player_value'] == 21:
        # 检查庄家是否也有21点
        dealer_full_value = calculate_hand_value(dealer_hand)
        session['game_state']['dealer_value'] = dealer_full_value
        session['game_state']['game_over'] = True

        if dealer_full_value == 21:
            session['game_state']['message'] = '平局！双方都是21点'
            session['game_state']['message_class'] = 'info'
        else:
            session['game_state']['message'] = '21点！你赢了！'
            session['game_state']['message_class'] = 'success'

    session.modified = True
    return session['game_state']


def game_update(session, action):
    """处理玩家动作并更新游戏状态

    根据21点规则：
    - 可以选择要牌(hit)或停牌(stand)
    - 超过21点立即输掉(bust)
    - 庄家必须在17点以下要牌
    - 庄家爆牌则玩家获胜
    - 都不爆牌时点数高者获胜
    """
    game_state = session.get('game_state', {})
    if not game_state:
        return new_game(session)

    if game_state['game_over']:
        return game_state

    if action == 'hit':
        # 玩家要牌
        if not game_state['deck']:
            # 如果牌用完了，重新洗牌
            game_state['deck'] = make_deck()
            random.shuffle(game_state['deck'])

        new_card = game_state['deck'].pop()
        game_state['player_hand'].append(new_card)
        game_state['player_value'] = calculate_hand_value(game_state['player_hand'])

        # 记录要牌
        session_id = session.get('session_id', '')
        if session_id:
            game_logger.log_blackjack_hit(session_id, new_card)

        if game_state['player_value'] > 21:
            # 玩家爆牌，立即输掉
            game_state['game_over'] = True
            game_state['dealer_value'] = calculate_hand_value(game_state['dealer_hand'])
            game_state['message'] = '爆牌！你输了'
            game_state['message_class'] = 'error'

            # 记录爆牌
            if session_id:
                game_logger.log_blackjack_bust(session_id, is_player=True)
        else:
            game_state['message'] = '选择要牌(Hit)或停牌(Stand)'
            game_state['message_class'] = 'info'

    elif action == 'stand':
        # 玩家停牌，庄家开始行动
        dealer_hand = game_state['dealer_hand']
        dealer_value = calculate_hand_value(dealer_hand)

        # 记录停牌和庄家暗牌
        session_id = session.get('session_id', '')
        dealer_additional_cards = []

        if session_id:
            game_logger.log_blackjack_stand(session_id, dealer_hand[1])

        # 庄家必须在17点以下要牌
        while dealer_value < 17:
            if not game_state['deck']:
                # 如果牌用完了，重新洗牌
                game_state['deck'] = make_deck()
                random.shuffle(game_state['deck'])

            new_card = game_state['deck'].pop()
            dealer_hand.append(new_card)
            dealer_additional_cards.append(new_card)
            dealer_value = calculate_hand_value(dealer_hand)

        # 记录庄家额外抽的牌
        if session_id and dealer_additional_cards:
            for card in dealer_additional_cards:
                card_display = game_logger.get_card_display_name(card)
                game_logger.log_game_action(session_id, f"Dealer gets {card_display}.")

        player_val = game_state['player_value']
        dealer_val = dealer_value

        game_state['game_over'] = True
        game_state['dealer_value'] = dealer_val

        # 判断胜负
        if dealer_val > 21:
            # 庄家爆牌，玩家获胜
            game_state['message'] = '庄家爆牌！你赢了！'
            game_state['message_class'] = 'success'
            if session_id:
                game_logger.log_blackjack_bust(session_id, is_player=False)
        elif player_val > dealer_val:
            # 玩家点数更高
            game_state['message'] = '你赢了！'
            game_state['message_class'] = 'success'
            if session_id:
                game_logger.log_blackjack_result(session_id, "win")
        elif player_val == dealer_val:
            # 平局
            game_state['message'] = '平局！'
            game_state['message_class'] = 'info'
            if session_id:
                game_logger.log_blackjack_result(session_id, "tie")
        else:
            # 庄家点数更高
            game_state['message'] = '庄家赢了！'
            game_state['message_class'] = 'error'
            if session_id:
                game_logger.log_blackjack_result(session_id, "lose")

    session.modified = True
    return game_state