import random
from playcard import make_deck, is_red, get_rank, RANK_MAP, SUITS

# Return two integers a<b in range(n), assuming n>=2
def rand_int_pair(n):
    a = random.randint(0,n-1)
    b = (a + random.randint(1, n-1))%n
    return (a,b) if a<b else (b,a)

def new_game(session):
    # Create a standard deck of 52 cards
    deck = make_deck()
    random.shuffle(deck)
    tableau = [[] for _ in range(8)]
    for i, card in enumerate(deck):
        tableau[i % 8].append(card)

    # make the deal easier because it is often difficult
    for i in range(8):
        column = tableau[i]
        n = 7 if i<4 else 6
        for _ in range(random.randint(2,3)):
            a, b = rand_int_pair(n)
            if get_rank(column[a]) < get_rank(column[b]):
                column[a], column[b] = column[b], column[a]

    session['game_state'] = {
        'cells': [[] for _ in range(4)],
        'piles': [[] for _ in range(4)],
        'tableau': tableau,
        'game_over': False,
        'message': '',
    }
    return


# Return (is_red, rank) for a card
def color_and_rank(card):
    return is_red(card), get_rank(card)

# Convert string to int, return -1 if conversion fails
def safe_int(digit):
    try:
        return int(digit)
    except ValueError:
        return -1



def game_update(session, action):
    game_state = session.get('game_state', {})
    if not game_state:
        return new_game(session)

    cells = game_state['cells']
    piles = game_state['piles']  # foundations
    tableau = game_state['tableau']

    # Clear any previous message
    game_state['message'] = ''
    game_state['message_class'] = ''

    # Helper function to check if a card can be placed on foundation
    def can_place_on_foundation(card):
        card_rank = get_rank(card)
        card_suit = card[1]

        # Each foundation pile is dedicated to a specific suit
        # Pile 0: Hearts, Pile 1: Spades, Pile 2: Diamonds, Pile 3: Clubs
        suit_to_pile = {'H': 0, 'S': 1, 'D': 2, 'C': 3}
        pile_index = suit_to_pile[card_suit]
        pile = piles[pile_index]

        if not pile:  # Empty foundation
            return card_rank == 1, pile_index  # Only Ace can start a foundation
        else:  # Foundation has cards
            return get_rank(pile[-1]) + 1 == card_rank, pile_index

    # Helper function to check if cards form a valid sequence for tableau movement
    def is_valid_sequence(cards):
        if len(cards) <= 1:
            return True

        for i in range(len(cards) - 1):
            current_card = cards[i]
            next_card = cards[i + 1]

            # Check alternating colors and descending ranks
            if (is_red(current_card) == is_red(next_card) or
                get_rank(current_card) != get_rank(next_card) + 1):
                return False
        return True

    # Helper function to calculate maximum movable cards based on free spaces
    def max_movable_cards(free_cells, free_columns):
        return (free_cells + 1) * (2 ** free_columns)

    # Helper function to count free spaces
    def count_free_spaces(exclude_target_col=None):
        free_cells = sum(1 for cell in cells if not cell)
        free_columns = 0
        for i, column in enumerate(tableau):
            if not column and i != exclude_target_col:
                free_columns += 1
        return free_cells, free_columns

    # - 'tt<source><target>': Move card(s) from column <source> to column <target> (0-based)
    if action.startswith('tt') and len(action) == 4:
        source_col = safe_int(action[2])
        target_col = safe_int(action[3])

        if (source_col < 0 or source_col >= 8 or target_col < 0 or target_col >= 8 or
            source_col == target_col):
            game_state['message'] = 'Invalid move'
            game_state['message_class'] = 'error'
        elif not tableau[source_col]:
            game_state['message'] = 'Source column is empty'
            game_state['message_class'] = 'error'
        else:
            source_column = tableau[source_col]
            target_column = tableau[target_col]

            # Find the longest valid sequence from the bottom of source column
            valid_sequence = []
            for i in range(len(source_column) - 1, -1, -1):
                test_sequence = source_column[i:]
                if is_valid_sequence(test_sequence):
                    valid_sequence = test_sequence
                else:
                    break

            if not valid_sequence:
                game_state['message'] = 'Card(s) not movable'
                game_state['message_class'] = 'error'
            else:
                # Check if the sequence can be placed on target column
                can_place = False
                if not target_column:  # Empty target column
                    can_place = True
                else:
                    # Check if first card of sequence can be placed on last card of target
                    target_card = target_column[-1]
                    source_card = valid_sequence[0]
                    if (is_red(target_card) != is_red(source_card) and
                        get_rank(target_card) == get_rank(source_card) + 1):
                        can_place = True

                if not can_place:
                    game_state['message'] = 'Card(s) not movable'
                    game_state['message_class'] = 'error'
                else:
                    # Calculate free spaces (excluding target column)
                    free_cells, free_columns = count_free_spaces(exclude_target_col=target_col)

                    max_cards = max_movable_cards(free_cells, free_columns)

                    if len(valid_sequence) > max_cards:
                        if target_column:  # Non-empty target
                            game_state['message'] = f'Your free slots only support moving {max_cards} cards while {len(valid_sequence)} are desired. No card was moved.'
                            game_state['message_class'] = 'error'
                        else:  # Empty target - move only one card
                            # Move only the last card (basic rule)
                            card_to_move = source_column.pop()
                            target_column.append(card_to_move)
                            game_state['message'] = 'Card moved between tableau columns'
                            game_state['message_class'] = 'success'
                    else:
                        # Move the entire valid sequence
                        cards_to_move = source_column[-len(valid_sequence):]
                        del source_column[-len(valid_sequence):]
                        target_column.extend(cards_to_move)
                        game_state['message'] = 'Card moved between tableau columns'
                        game_state['message_class'] = 'success'

    # - 'tc<source><target>': Move card from column <source> to cell <target> (0-based)
    elif action.startswith('tc') and len(action) == 4:
        source_col = safe_int(action[2])
        target_cell = safe_int(action[3])

        if (source_col < 0 or source_col >= 8 or target_cell < 0 or target_cell >= 4):
            game_state['message'] = 'Invalid move'
            game_state['message_class'] = 'error'
        elif not tableau[source_col]:
            game_state['message'] = 'Source column is empty'
            game_state['message_class'] = 'error'
        elif cells[target_cell]:
            game_state['message'] = 'Target cell is occupied'
            game_state['message_class'] = 'error'
        else:
            # Move the last card from column to cell
            card = tableau[source_col].pop()
            cells[target_cell].append(card)
            game_state['message'] = 'Card moved from tableau to cell'
            game_state['message_class'] = 'success'

    # - 'ct<source><target>': Move card from cell <source> to column <target> (0-based)
    elif action.startswith('ct') and len(action) == 4:
        source_cell = safe_int(action[2])
        target_col = safe_int(action[3])

        if (source_cell < 0 or source_cell >= 4 or target_col < 0 or target_col >= 8):
            game_state['message'] = 'Invalid move'
            game_state['message_class'] = 'error'
        elif not cells[source_cell]:
            game_state['message'] = 'Source cell is empty'
            game_state['message_class'] = 'error'
        else:
            source_card = cells[source_cell][0]
            target_column = tableau[target_col]

            can_place = False
            if not target_column:  # Empty column
                can_place = True
            else:
                # Check if card can be placed on last card of target column
                target_card = target_column[-1]
                if (is_red(target_card) != is_red(source_card) and
                    get_rank(target_card) == get_rank(source_card) + 1):
                    can_place = True

            if can_place:
                card = cells[source_cell].pop()
                target_column.append(card)
                game_state['message'] = 'Card moved from cell to tableau'
                game_state['message_class'] = 'success'
            else:
                game_state['message'] = 'Card(s) not movable'
                game_state['message_class'] = 'error'

    # - 'tf<column>': Move last card from column <column> (0-based) to foundation
    elif action.startswith('tf') and len(action) == 3:
        source_col = safe_int(action[2])

        if source_col < 0 or source_col >= 8:
            game_state['message'] = 'Invalid column'
            game_state['message_class'] = 'error'
        elif not tableau[source_col]:
            game_state['message'] = 'Column is empty'
            game_state['message_class'] = 'error'
        else:
            card = tableau[source_col][-1]
            can_place, pile_index = can_place_on_foundation(card)

            if can_place:
                card = tableau[source_col].pop()
                piles[pile_index].append(card)
                game_state['message'] = 'Card moved to foundation'
                game_state['message_class'] = 'success'

                # Check for win condition (simple version - all cards in foundations)
                if sum(len(pile) for pile in piles) == 52:
                    game_state['game_over'] = True
                    game_state['message'] = 'Congratulations! You won!'
                    game_state['message_class'] = 'success'
            else:
                game_state['message'] = 'Card does not meet foundation requirements'
                game_state['message_class'] = 'error'

    # - 'cf<cell>': Move card from cell <cell> (0-based) to foundation
    elif action.startswith('cf') and len(action) == 3:
        source_cell = safe_int(action[2])

        if source_cell < 0 or source_cell >= 4:
            game_state['message'] = 'Invalid cell'
            game_state['message_class'] = 'error'
        elif not cells[source_cell]:
            game_state['message'] = 'Cell is empty'
            game_state['message_class'] = 'error'
        else:
            card = cells[source_cell][0]
            can_place, pile_index = can_place_on_foundation(card)

            if can_place:
                card = cells[source_cell].pop()
                piles[pile_index].append(card)
                game_state['message'] = 'Card moved to foundation'
                game_state['message_class'] = 'success'

                # Check for win condition (simple version - all cards in foundations)
                if sum(len(pile) for pile in piles) == 52:
                    game_state['game_over'] = True
                    game_state['message'] = 'Congratulations! You won!'
                    game_state['message_class'] = 'success'
            else:
                game_state['message'] = 'Card does not meet foundation requirements'
                game_state['message_class'] = 'error'

    else:
        game_state['message'] = 'Invalid action'
        game_state['message_class'] = 'error'

    # game state has changed itself so tell session it has changed
    session.modified = True
    return

