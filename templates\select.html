{% extends "base.html" %}

{% block main_content %}
    <div class="select-container">
        <div class="select-header">
            <h2>🎮 选择您的游戏</h2>
            <div class="warning-banner">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">选择新游戏将会重置当前进度</span>
            </div>
        </div>

        <div class="games-grid">
            <div class="game-card {{ 'active' if cur_game=='blackjack' else '' }}"
                 onclick="selectGame('blackjack')">
                <div class="game-icon">🃏</div>
                <div class="game-content">
                    <h3 class="game-title">21点</h3>
                    <p class="game-description">经典纸牌游戏，与庄家比拼点数，目标是获得21点或更接近21点而不爆牌</p>
                    <div class="game-features">
                        <span class="feature-tag">策略</span>
                        <span class="feature-tag">运气</span>
                        <span class="feature-tag">快节奏</span>
                    </div>
                    <div class="game-stats">
                        <div class="stat">
                            <span class="stat-label">难度</span>
                            <div class="difficulty-bar">
                                <div class="difficulty-fill" style="width: 60%"></div>
                            </div>
                        </div>
                        <div class="stat">
                            <span class="stat-label">时长</span>
                            <span class="stat-value">2-5分钟</span>
                        </div>
                    </div>
                </div>
                {% if cur_game=='blackjack' %}
                <div class="current-game-badge">当前游戏</div>
                {% endif %}
            </div>

            <div class="game-card {{ 'active' if cur_game=='freecell' else '' }}"
                 onclick="selectGame('freecell')">
                <div class="game-icon">🎯</div>
                <div class="game-content">
                    <h3 class="game-title">空当接龙</h3>
                    <p class="game-description">经典单人纸牌游戏，运用策略将所有牌按花色和顺序移动到基础堆</p>
                    <div class="game-features">
                        <span class="feature-tag">策略</span>
                        <span class="feature-tag">逻辑</span>
                        <span class="feature-tag">耐心</span>
                    </div>
                    <div class="game-stats">
                        <div class="stat">
                            <span class="stat-label">难度</span>
                            <div class="difficulty-bar">
                                <div class="difficulty-fill" style="width: 80%"></div>
                            </div>
                        </div>
                        <div class="stat">
                            <span class="stat-label">时长</span>
                            <span class="stat-value">10-30分钟</span>
                        </div>
                    </div>
                </div>
                {% if cur_game=='freecell' %}
                <div class="current-game-badge">当前游戏</div>
                {% endif %}
            </div>
        </div>

        <div class="coming-soon">
            <h3>🚀 即将推出</h3>
            <div class="coming-soon-games">
                <div class="coming-game">
                    <span class="coming-icon">🏁</span>
                    <span class="coming-name">象棋</span>
                </div>
                <div class="coming-game">
                    <span class="coming-icon">🎲</span>
                    <span class="coming-name">骰子游戏</span>
                </div>
                <div class="coming-game">
                    <span class="coming-icon">🃏</span>
                    <span class="coming-name">德州扑克</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectGame(gameName) {
            window.location.href = `/select_game/${gameName}`;
        }
    </script>
{% endblock %}