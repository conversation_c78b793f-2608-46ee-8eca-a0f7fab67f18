
.dealer-area,
.player-area {
    margin-bottom: 30px;
    text-align: center;
}

.dealer-area h2,
.player-area h2 {
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #fff;
}

/* Cards */
.cards {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 20px;
    margin: 20px 0;
    justify-content: center;
}

/* Card sprite styles for 52-card deck */
.card {
    width: 10vw; /* ~138px at 1280px */
    aspect-ratio: 169 / 244;
    padding: 0;
    border: 0;
    display: inline-block;
}

/* Message */
.message {
    text-align: center;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 20px;
    border-radius: 10px;
    margin: 30px 0;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.6);
    border: 3px solid #ffcc00;
}

.message h3 {
    font-size: 2.8rem;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.success h3 {
    color: #00ff00;
    /* Bright green for wins */
}

.error h3 {
    color: #ff6b6b;
    /* Red for losses */
}

.info h3 {
    color: #ffcc00;
    /* Gold for info/ties */
}

/* Action Buttons */
.actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    text-align: center;
}

.btn {
    display: inline-block;
    padding: 15px 30px;
    background-color: #ffcc00;
    color: #000;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    margin: 10px;
}

.btn:hover {
    background-color: #e6b800;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .cards {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .actions {
        flex-direction: column;
        align-items: center;
    }

    .btn,
    a {
        width: 100%;
        max-width: 200px;
        text-align: center;
        margin-bottom: 10px;
    }
}