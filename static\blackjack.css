/* Blackjack Game Styles */
.blackjack-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    color: white;
    min-height: 80vh;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(255,255,255,0.2);
}

.game-header h1 {
    font-size: 2.5rem;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.status-badge.playing {
    background-color: #28a745;
    color: white;
}

.status-badge.finished {
    background-color: #dc3545;
    color: white;
}

.game-board {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.dealer-section, .player-section {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.player-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.player-avatar {
    font-size: 3rem;
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid rgba(255,255,255,0.3);
}

.player-details h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.score-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.score-label {
    font-size: 1rem;
    opacity: 0.8;
}

.score-value {
    font-size: 2rem;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.score-value.bust {
    color: #ff6b6b;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.cards-container {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.card-wrapper {
    position: relative;
    transition: transform 0.3s ease;
}

.card-wrapper:hover {
    transform: translateY(-10px);
}

.card {
    width: 120px;
    aspect-ratio: 169 / 244;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
    border-radius: 8px;
}

.hidden-card {
    position: relative;
}

.hidden-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    font-weight: bold;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    background: rgba(0,0,0,0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-message {
    text-align: center;
    padding: 1.5rem;
    border-radius: 15px;
    margin: 1rem 0;
    backdrop-filter: blur(10px);
    border: 2px solid;
}

.game-message.success {
    background: rgba(40, 167, 69, 0.2);
    border-color: #28a745;
}

.game-message.error {
    background: rgba(220, 53, 69, 0.2);
    border-color: #dc3545;
}

.game-message.info {
    background: rgba(255, 193, 7, 0.2);
    border-color: #ffc107;
}

.message-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.message-icon {
    font-size: 2rem;
}

.message-text {
    font-size: 1.3rem;
    font-weight: bold;
}

.game-actions {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    min-width: 160px;
    justify-content: center;
}

.hit-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.hit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
    border-color: rgba(255,255,255,0.3);
}

.stand-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.stand-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(116, 185, 255, 0.4);
    border-color: rgba(255,255,255,0.3);
}

.new-game-btn {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
}

.new-game-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 184, 148, 0.4);
    border-color: rgba(255,255,255,0.3);
}

.btn-icon {
    font-size: 1.3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .blackjack-container {
        padding: 1rem;
        margin: 1rem;
    }
    
    .game-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .game-header h1 {
        font-size: 2rem;
    }
    
    .player-info {
        flex-direction: column;
        text-align: center;
    }
    
    .cards-container {
        gap: 0.5rem;
    }
    
    .card {
        width: 80px;
    }
    
    .game-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .card {
        width: 60px;
    }
    
    .player-avatar {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }
    
    .score-value {
        font-size: 1.5rem;
    }
}
