import os
import uuid
from flask import Flask, render_template, redirect, url_for, session, jsonify
from playcard import get_card_name
import blackjack, freecell
import game_logger


SUPPORTED_GAMES = {'blackjack': blackjack, 'freecell': freecell}
app = Flask(__name__)
# Generate a random secret key for the session
app.secret_key = os.environ.get('FLASK_SECRET_KEY', os.urandom(24))

@app.route('/')
def index():
    return redirect(url_for('game'))

@app.route('/select')
def select():
    session.setdefault('session_id', uuid.uuid4().hex)
    return render_template('select.html', cur_game=session.get('cur_game', ''))

@app.route('/new_game')
def new_game():
    cur_game = session.get('cur_game', '')
    if cur_game in SUPPORTED_GAMES:
        SUPPORTED_GAMES[cur_game].new_game(session)
        session.modified = True
        return redirect(url_for('game'))
    else:
        return redirect(url_for('select'))

@app.route('/game')
def game():
    session.setdefault('session_id', uuid.uuid4().hex)
    cur_game = session.get('cur_game', '')
    game_state = session.get('game_state', {})
    if cur_game in SUPPORTED_GAMES and game_state:
        return render_template(f'{cur_game}.html',
                               game_state=game_state)
    else:
        return redirect(url_for('select'))

@app.route('/game_update/<action>')
def game_update(action):
    cur_game = session.get('cur_game', '')
    if cur_game in SUPPORTED_GAMES:
        SUPPORTED_GAMES[cur_game].game_update(session, action)
        session.modified = True
        return redirect(url_for('game'))
    else:
        return redirect(url_for('select'))


@app.route('/select_game/<target_game>')
def select_game(target_game):
    if target_game in SUPPORTED_GAMES:
        session['cur_game'] = target_game
        session_id = session.setdefault('session_id', uuid.uuid4().hex)

        # 记录游戏选择
        game_logger.log_game_selection(session_id, target_game)

        SUPPORTED_GAMES[target_game].new_game(session)
        return redirect(url_for('game'))
    else:
        return render_template('about.html', supported=False)


@app.route('/rules')
def rules():
    return render_template('rules.html',
                           cur_game=session.get('cur_game', ''))

@app.route('/log')
def log():
    session_id = session.setdefault('session_id', uuid.uuid4().hex)
    log_content = game_logger.get_game_log(session_id)
    return render_template('userlog.html', log=log_content)

@app.route('/clear_log', methods=['POST'])
def clear_log():
    session_id = session.get('session_id', '')
    if not session_id:
        return jsonify({'success': False, 'error': 'No session'})

    try:
        game_logger.clear_log(session_id)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/about')
def about():
    return render_template('about.html', supported=True)


@app.context_processor
def utility_processor():
    # Make the `get_card_name` function available in all templates
    return dict(get_card_name=get_card_name, enumerate=enumerate)


if __name__ == '__main__':
    app.run(port=80)