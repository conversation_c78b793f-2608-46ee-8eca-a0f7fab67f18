import os
import json
from datetime import datetime, timedelta
from playcard import get_card_name

def get_china_time():
    """获取中国时区的当前时间（UTC+8）"""
    # 使用本地时间，假设系统已设置为中国时区
    return datetime.now()

def format_time(dt):
    """格式化时间为 [YYYY-MM-DD HH:MM:SS] 格式"""
    return dt.strftime('[%Y-%m-%d %H:%M:%S]')

def get_card_display_name(card):
    """获取卡牌的显示名称"""
    rank_names = {
        'A': '1', '2': '2', '3': '3', '4': '4', '5': '5', '6': '6', '7': '7', '8': '8', '9': '9',
        'T': '10', 'J': 'jack', 'Q': 'queen', 'K': 'king'
    }
    suit_names = {
        'H': 'heart', 'S': 'spade', 'D': 'diamond', 'C': 'club'
    }
    
    rank = card[0]
    suit = card[1]
    
    return f"{suit_names[suit]}_{rank_names[rank]}"

def ensure_log_file(session_id):
    """确保日志文件存在"""
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f'{session_id}.log')
    if not os.path.exists(log_file):
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write('')  # 创建空文件
    
    return log_file

def log_game_action(session_id, message):
    """记录游戏操作到日志文件"""
    log_file = ensure_log_file(session_id)
    timestamp = format_time(get_china_time())
    log_entry = f"{timestamp} {message}\n"
    
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(log_entry)

def get_game_log(session_id):
    """获取用户的游戏日志"""
    log_file = ensure_log_file(session_id)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return ""

# Blackjack 日志函数
def log_blackjack_new_game(session_id, dealer_card, player_cards):
    """记录新的21点游戏开始"""
    dealer_display = get_card_display_name(dealer_card)
    player_card1 = get_card_display_name(player_cards[0])
    player_card2 = get_card_display_name(player_cards[1])
    
    message = f"New Blackjack game. Dealer gets {dealer_display} and another card. Player gets {player_card1} and {player_card2}."
    log_game_action(session_id, message)

def log_blackjack_hit(session_id, card):
    """记录玩家要牌"""
    card_display = get_card_display_name(card)
    message = f"Player hits and gets {card_display}."
    log_game_action(session_id, message)

def log_blackjack_stand(session_id, dealer_hidden_card, dealer_additional_cards=None):
    """记录玩家停牌和庄家行动"""
    hidden_display = get_card_display_name(dealer_hidden_card)
    message = f"Player stands. Dealer's hidden card is {hidden_display}."
    log_game_action(session_id, message)
    
    if dealer_additional_cards:
        for card in dealer_additional_cards:
            card_display = get_card_display_name(card)
            log_game_action(session_id, f"Dealer gets {card_display}.")

def log_blackjack_bust(session_id, is_player=True):
    """记录爆牌"""
    if is_player:
        message = "Player busts and loses."
    else:
        message = "Dealer busts. Player wins."
    log_game_action(session_id, message)

def log_blackjack_result(session_id, result):
    """记录游戏结果"""
    if result == "win":
        message = "Player wins."
    elif result == "lose":
        message = "Dealer wins."
    elif result == "tie":
        message = "It's a tie."
    elif result == "blackjack":
        message = "Player gets Blackjack and wins."
    
    log_game_action(session_id, message)

# Freecell 日志函数
def log_freecell_new_game(session_id, tableau):
    """记录新的空当接龙游戏开始"""
    tableau_str = str(tableau).replace("'", "")
    message = f"New Freecell game, tableau={tableau_str}."
    log_game_action(session_id, message)

def log_freecell_move_to_foundation(session_id, source_type, source_index):
    """记录移动到基础堆"""
    if source_type == "tableau":
        message = f"Tableau column {source_index}'s top card moved to foundation."
    elif source_type == "cell":
        message = f"Cell {source_index}'s card moved to foundation."
    
    log_game_action(session_id, message)

def log_freecell_move_between_columns(session_id, source_col, target_col):
    """记录列间移动"""
    message = f"Tableau column {source_col}'s top card moved to column {target_col}."
    log_game_action(session_id, message)

def log_freecell_move_to_cell(session_id, source_col, target_cell):
    """记录移动到单元格"""
    message = f"Tableau column {source_col}'s top card moved to cell {target_cell}."
    log_game_action(session_id, message)

def log_freecell_move_from_cell(session_id, source_cell, target_col):
    """记录从单元格移动"""
    message = f"Cell {source_cell}'s card moved to column {target_col}."
    log_game_action(session_id, message)

def log_freecell_win(session_id):
    """记录空当接龙获胜"""
    message = "Player wins Freecell game!"
    log_game_action(session_id, message)

def log_game_selection(session_id, game_name):
    """记录游戏选择"""
    message = f"Select {game_name}."
    log_game_action(session_id, message)

def clear_log(session_id):
    """清空指定会话的游戏日志"""
    log_file = ensure_log_file(session_id)
    with open(log_file, 'w', encoding='utf-8') as f:
        f.write("")  # 清空文件内容
    return True
