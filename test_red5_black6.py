#!/usr/bin/env python3
"""
Test script to verify that red 5 can be placed on black 6
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from freecell import game_update
from playcard import get_rank, is_red

def test_red5_on_black6():
    """Test that red 5 can be placed on black 6"""
    print("Testing red 5 on black 6...")
    
    # Create a test session
    session = {'modified': False}
    
    # Create a simple game state
    session['game_state'] = {
        'cells': [[], [], [], []],  # All cells empty
        'piles': [[], [], [], []],  # All foundations empty
        'tableau': [
            ['5H'],      # Column 0: Red 5 (Hearts)
            ['6C'],      # Column 1: Black 6 (Clubs)
            [],          # Column 2: Empty
            [],          # Column 3: Empty
            [],          # Column 4: Empty
            [],          # Column 5: Empty
            [],          # Column 6: Empty
            []           # Column 7: Empty
        ],
        'game_over': False,
        'message': '',
        'message_class': ''
    }
    
    print("Initial state:")
    print(f"  Column 0: {session['game_state']['tableau'][0]} (Red 5)")
    print(f"  Column 1: {session['game_state']['tableau'][1]} (Black 6)")
    
    # Test the move: tt01 (move from column 0 to column 1)
    print("\nAttempting to move red 5 to black 6 (tt01)...")
    game_update(session, 'tt01')
    
    print(f"Result: {session['game_state']['message']}")
    print(f"Message class: {session['game_state']['message_class']}")
    
    # Check final state
    print("\nFinal state:")
    print(f"  Column 0: {session['game_state']['tableau'][0]}")
    print(f"  Column 1: {session['game_state']['tableau'][1]}")
    
    # Verify the move was successful
    if (session['game_state']['message_class'] == 'success' and 
        len(session['game_state']['tableau'][1]) == 2 and
        session['game_state']['tableau'][1] == ['6C', '5H']):
        print("\n✓ SUCCESS: Red 5 was correctly placed on Black 6!")
        return True
    else:
        print("\n✗ FAILED: Red 5 was not placed on Black 6!")
        return False

def test_invalid_move():
    """Test an invalid move (same color)"""
    print("\n" + "="*50)
    print("Testing invalid move (red on red)...")
    
    # Create a test session
    session = {'modified': False}
    
    # Create a simple game state
    session['game_state'] = {
        'cells': [[], [], [], []],  # All cells empty
        'piles': [[], [], [], []],  # All foundations empty
        'tableau': [
            ['5H'],      # Column 0: Red 5 (Hearts)
            ['6H'],      # Column 1: Red 6 (Hearts) - same color!
            [],          # Column 2: Empty
            [],          # Column 3: Empty
            [],          # Column 4: Empty
            [],          # Column 5: Empty
            [],          # Column 6: Empty
            []           # Column 7: Empty
        ],
        'game_over': False,
        'message': '',
        'message_class': ''
    }
    
    print("Initial state:")
    print(f"  Column 0: {session['game_state']['tableau'][0]} (Red 5)")
    print(f"  Column 1: {session['game_state']['tableau'][1]} (Red 6)")
    
    # Test the move: tt01 (move from column 0 to column 1)
    print("\nAttempting to move red 5 to red 6 (should fail)...")
    game_update(session, 'tt01')
    
    print(f"Result: {session['game_state']['message']}")
    print(f"Message class: {session['game_state']['message_class']}")
    
    # Verify the move was rejected
    if (session['game_state']['message_class'] == 'error' and 
        len(session['game_state']['tableau'][1]) == 1):
        print("\n✓ SUCCESS: Invalid move was correctly rejected!")
        return True
    else:
        print("\n✗ FAILED: Invalid move was not rejected!")
        return False

def main():
    """Run all tests"""
    print("=== Testing Red 5 on Black 6 Fix ===\n")
    
    try:
        test1_passed = test_red5_on_black6()
        test2_passed = test_invalid_move()
        
        print("\n=== Test Summary ===")
        if test1_passed and test2_passed:
            print("✓ All tests passed! The fix is working correctly.")
        else:
            print("✗ Some tests failed. Please check the implementation.")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
