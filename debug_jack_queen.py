#!/usr/bin/env python3
"""
Debug script to test <PERSON> on Queen move
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from freecell import game_update
from playcard import get_rank, is_red, RANK_MAP

def debug_jack_queen():
    """Debug Jack on Queen move"""
    print("=== Debugging Jack on Queen Move ===")
    
    # Test card properties
    jack_spades = 'JS'
    queen_diamonds = 'QD'
    
    print(f"Jack of Spades: {jack_spades}")
    print(f"  - Rank: {get_rank(jack_spades)} (should be 11)")
    print(f"  - Is Red: {is_red(jack_spades)} (should be False)")
    
    print(f"Queen of Diamonds: {queen_diamonds}")
    print(f"  - Rank: {get_rank(queen_diamonds)} (should be 12)")
    print(f"  - Is Red: {is_red(queen_diamonds)} (should be True)")
    
    # Test placement logic
    print(f"\nPlacement logic test:")
    print(f"  - Different colors: {is_red(queen_diamonds) != is_red(jack_spades)} (should be True)")
    print(f"  - Jack rank == Queen rank - 1: {get_rank(jack_spades) == get_rank(queen_diamonds) - 1} (should be True)")
    
    # Create a test session (mock Flask session)
    class MockSession(dict):
        def __init__(self):
            super().__init__()
            self.modified = False

    session = MockSession()
    
    # Create a simple game state with Jack and Queen
    session['game_state'] = {
        'cells': [[], [], [], []],  # All cells empty
        'piles': [[], [], [], []],  # All foundations empty
        'tableau': [
            ['JS'],      # Column 0: Jack of Spades
            ['QD'],      # Column 1: Queen of Diamonds
            [],          # Column 2: Empty
            [],          # Column 3: Empty
            [],          # Column 4: Empty
            [],          # Column 5: Empty
            [],          # Column 6: Empty
            []           # Column 7: Empty
        ],
        'game_over': False,
        'message': '',
        'message_class': ''
    }
    
    print(f"\nInitial game state:")
    print(f"  Column 0: {session['game_state']['tableau'][0]} (Jack of Spades)")
    print(f"  Column 1: {session['game_state']['tableau'][1]} (Queen of Diamonds)")
    
    # Test the move: tt01 (move from column 0 to column 1)
    print(f"\nAttempting move tt01 (Jack of Spades to Queen of Diamonds)...")
    
    # Let's manually trace through the logic
    source_col = 0
    target_col = 1
    source_column = session['game_state']['tableau'][source_col]
    target_column = session['game_state']['tableau'][target_col]
    
    print(f"Source column: {source_column}")
    print(f"Target column: {target_column}")
    
    # Test is_valid_sequence function
    from freecell import game_update
    
    # We need to access the inner function, so let's recreate the logic here
    def is_valid_sequence(cards):
        if len(cards) <= 1:
            return True
        
        for i in range(len(cards) - 1):
            current_card = cards[i]
            next_card = cards[i + 1]
            
            # Check alternating colors and descending ranks
            if (is_red(current_card) == is_red(next_card) or
                get_rank(current_card) != get_rank(next_card) + 1):
                return False
        return True
    
    # Find the longest valid sequence from the bottom of source column
    valid_sequence = []
    for i in range(len(source_column) - 1, -1, -1):
        test_sequence = source_column[i:]
        print(f"Testing sequence: {test_sequence}")
        if is_valid_sequence(test_sequence):
            valid_sequence = test_sequence
            print(f"  -> Valid sequence found: {valid_sequence}")
        else:
            print(f"  -> Invalid sequence")
            break
    
    print(f"Final valid sequence: {valid_sequence}")
    
    if valid_sequence:
        # Check if the sequence can be placed on target column
        target_card = target_column[-1]
        source_card = valid_sequence[0]
        
        print(f"Checking placement:")
        print(f"  Target card: {target_card} (rank={get_rank(target_card)}, red={is_red(target_card)})")
        print(f"  Source card: {source_card} (rank={get_rank(source_card)}, red={is_red(source_card)})")
        
        color_check = is_red(target_card) != is_red(source_card)
        rank_check = get_rank(source_card) == get_rank(target_card) - 1
        
        print(f"  Color check (different colors): {color_check}")
        print(f"  Rank check (source = target - 1): {rank_check}")
        print(f"  Can place: {color_check and rank_check}")
    
    # Now run the actual game update
    print(f"\nRunning actual game_update...")
    game_update(session, 'tt01')
    
    print(f"Result: {session['game_state']['message']}")
    print(f"Message class: {session['game_state']['message_class']}")
    
    # Check final state
    print(f"\nFinal state:")
    print(f"  Column 0: {session['game_state']['tableau'][0]}")
    print(f"  Column 1: {session['game_state']['tableau'][1]}")

if __name__ == "__main__":
    debug_jack_queen()
